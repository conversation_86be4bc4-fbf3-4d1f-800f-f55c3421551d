<?php

namespace App\Services;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesService
{

    private readonly array $selections;
    private readonly array $groupBy;


    private function __construct(
        private readonly SaleDistribution $saleDistribution,
        private readonly ?DistributionType $distributionType = null
    )
    {
        $this->selections = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
            DB::raw('CAST(SUM(crm_sales_details.quantity) AS DECIMAL(64,20)) /
                   SUM(SUM(crm_sales_details.quantity)) OVER (PARTITION BY crm_sales.product_id) AS percentage')
        ];

        $this->groupBy = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
        ];

        Log::debug('SalesService: Initialized'.collect([
            'sale_distribution' => $saleDistribution->name,
            'distribution_type' => $distributionType?->value,
            'selections_count' => count($this->selections),
            'group_by_count' => count($this->groupBy),
            'instance_id' => spl_object_id($this),
            'context' => 'SERVICE_INITIALIZATION'
        ])->toJson(JSON_PRETTY_PRINT));
    }

    public static function make(SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): static
    {
        Log::debug('SalesService: Creating new instance', [
            'sale_distribution' => $saleDistribution->name,
            'distribution_type' => $distributionType?->value,
            'context' => 'SERVICE_CREATION'
        ]);

        return new static(SaleDistribution::NORMAL, DistributionType::STORES);
    }

    private function generateCacheKey(string $functionName, array $parameters): string
    {
        // Include all instance state in cache key to prevent conflicts in Octane environments
        $cacheKeyData = [
            'function' => $functionName,
            'parameters' => $parameters,
            'distribution_type' => $this->distributionType?->value,
            'sale_distribution' => $this->saleDistribution->name,
            'selections' => $this->selections,
            'group_by' => $this->groupBy,
            // Add instance identifier to ensure cache isolation between different service instances
            'instance_hash' => spl_object_id($this)
        ];

        $paramsString = serialize($cacheKeyData);
        $key = $functionName . '|' . $paramsString;
        $hashedKey = md5($key);

        Log::debug('SalesService: Generated cache key with enhanced isolation', [
            'function_name' => $functionName,
            'parameters_count' => count($parameters),
            'distribution_type' => $this->distributionType?->value,
            'sale_distribution' => $this->saleDistribution->name,
            'selections_count' => count($this->selections),
            'group_by_count' => count($this->groupBy),
            'instance_id' => spl_object_id($this),
            'serialized_length' => strlen($paramsString),
            'cache_key_hash' => $hashedKey
        ]);

        return $hashedKey;
    }

    public function getRatiosForDistribution(string $date, int $product, array $distributorIds, array $divisionIds = []): Collection
    {
        $date = "2025-05-01";
        $product = 41;
        $distributorIds = [1,2,3,4,5];
        // Validate input parameters
        if (empty($distributorIds)) {
            Log::warning('SalesService: Empty distributor IDs provided', [
                'date' => $date,
                'product_id' => $product,
                'context' => 'PARAMETER_VALIDATION'
            ]);
            return collect();
        }

        $date = Carbon::parse($date);

        DB::statement("set sql_mode=''");

//        $cacheKey = $this->generateCacheKey(__FUNCTION__, [$this->saleDistribution, ...$this->selections, ...func_get_args()]);

//        Log::debug('SalesService: Cache key generated', [
//            'cache_key' => $cacheKey,
//            'cache_ttl_hours' => 2
//        ]);

        // Reduce cache TTL for distribution queries to prevent stale data issues
//        $cacheTTL = now()->addMinutes(30); // Reduced from 2 hours to 30 minutes

//        return Cache::remember(
//            $cacheKey,
//            $cacheTTL,
//            function () use ($date, $distributorIds, $product, $divisionIds) {


                $query = Sale::query();
                $query->select($this->selections);
                $this->buildJoins($query,$date, $product, $divisionIds);
                $ceilings = $this->getCeiling();

                $query
                    ->whereYear("sales.date", (string)$date->year)
                    ->whereMonth("sales.date", (string)$date->month)
                    ->whereIn("sales.distributor_id", $distributorIds)
                    ->whereIn("sales.ceiling", $ceilings);
                $query->groupBy($this->groupBy);


                try {
                    $results = $query->get();
                } catch (\Exception $e) {
                    Log::error('SalesService: Query execution failed', [
                        'date' => $date->format('Y-m-d'),
                        'product_id' => $product,
                        'distributor_ids' => $distributorIds,
                        'division_ids' => $divisionIds,
                        'error' => $e->getMessage(),
                        'sql' => $query->toSql(),
                        'bindings' => $query->getBindings(),
                        'context' => 'QUERY_EXECUTION_ERROR'
                    ]);
                    return collect();
                }

                // Log detailed results for debugging
                Log::info('SalesService: Distribution ratios retrieved'.collect([
                    'date' => $date->format('Y-m-d'),
                    'product_id' => $product,
                    'ratios_count' => $results->count(),
                    'total_percentage' => $results->sum('percentage'),
                    'distribution_type' => $this->distributionType?->value ?? 'default',
                    'sale_distribution' => $this->saleDistribution->name,
                    'sample_results' => $results->take(3)->toArray(), // Show first 3 results for debugging
                    'context' => 'SUCCESSFUL_QUERY_EXECUTION'
                ])->toJson(JSON_PRETTY_PRINT));

                // If no results, log additional debugging info
                if ($results->count() === 0) {
                    // Check if there are any sales at all for these parameters
                    $salesCount = Sale::where('product_id', $product)
                        ->whereYear('date', $date->year)
                        ->whereMonth('date', $date->month)
                        ->whereIn('distributor_id', $distributorIds)
                        ->count();

                    $salesWithDetailsCount = Sale::where('product_id', $product)
                        ->whereYear('date', $date->year)
                        ->whereMonth('date', $date->month)
                        ->whereIn('distributor_id', $distributorIds)
                        ->whereHas('details')
                        ->count();

                    $salesWithCorrectCeiling = Sale::where('product_id', $product)
                        ->whereYear('date', $date->year)
                        ->whereMonth('date', $date->month)
                        ->whereIn('distributor_id', $distributorIds)
                        ->whereIn('ceiling', array_map(fn($c) => $c->value, $ceilings))
                        ->count();

                    Log::warning('SalesService: No distribution ratios found - investigating'.collect([
                        'date' => $date->format('Y-m-d'),
                        'product_id' => $product,
                        'distributor_ids' => $distributorIds,
                        'division_ids' => $divisionIds,
                        'distribution_type' => $this->distributionType?->value,
                        'ceilings' => array_map(fn($c) => $c->value, $ceilings),
                        'query_conditions' => [
                            'year' => $date->year,
                            'month' => $date->month,
                            'has_distribution_type' => $this->distributionType !== null,
                            'has_division_filter' => !empty($divisionIds)
                        ],
                        'database_state' => [
                            'total_sales_count' => $salesCount,
                            'sales_with_details_count' => $salesWithDetailsCount,
                            'sales_with_correct_ceiling_count' => $salesWithCorrectCeiling
                        ],
                        'context' => 'EMPTY_RESULTS_INVESTIGATION'
                    ])->toJson(JSON_PRETTY_PRINT));
                }

                return $results;
//            }
//        );
    }

    private function buildJoins($query,Carbon $date, int $product, array $divisionIds): void
    {

        $query
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("line_products", function ($join) use ($date, $product) {
                $join
                    ->on("sales.product_id", "=", "line_products.product_id")
                    ->where("line_products.product_id", $product)
                    ->whereColumn("line_products.line_id", "sales_details.line_id")
                    ->where("line_products.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_products.to_date", ">", $date)
                            ->orWhereNull("line_products.to_date")
                    );
            })
            ->join("line_divisions", function ($join) use ($date, $divisionIds) {
                $join
                    ->on("sales_details.div_id", "=", "line_divisions.id")
                    ->whereColumn("line_divisions.line_id", "line_products.line_id")
                    ->where("line_divisions.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_divisions.to_date", ">", $date)
                            ->orWhereNull("line_divisions.to_date")
                    );

                if (!empty($divisionIds)) {
                    $join->whereIn('line_divisions.id', $divisionIds);
                    Log::debug('SalesService: Applied division ID filter', [
                        'division_ids' => $divisionIds
                    ]);
                }
            });

        // Add mappings joins when DistributionType is specified
        if ($this->distributionType !== null) {

            $query
                ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
                ->join("mappings", function ($join) {
                    $join->on('mapping_sale.mapping_id', 'mappings.id');

                    if ($this->distributionType === DistributionType::PRIVATE_PHARMACY) {
                        Log::debug('SalesService: Applying PRIVATE_PHARMACY mapping filter (excluding STORES and LOCAL_CHAINS)');
                        $join->where(function ($q) {
                            $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                                ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                                ->orWhere('mappings.unified_pharmacy_type_id', null);
                        });
                    } else {
                        Log::debug('SalesService: Applying specific distribution type mapping filter', [
                            'distribution_type_value' => $this->distributionType->value
                        ]);
                        $join->where('mappings.unified_pharmacy_type_id', $this->distributionType->value);
                    }
                })
                ->where("mappings.exception", false);
        } else {
            Log::debug('SalesService: No distribution type specified, skipping mappings joins');
        }

        if ($this->saleDistribution == SaleDistribution::NORMAL) {
            // Use LEFT JOIN for STORES distribution type, INNER JOIN for others (matching DistributionService logic)
            $joinType = ($this->distributionType === DistributionType::STORES) ? 'leftJoin' : 'join';

            $query->$joinType("product_ceilings", function ($join) use ($date) {
                $join
                    ->on("product_ceilings.product_id", "=", "sales.product_id")
                    ->where("product_ceilings.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("product_ceilings.to_date", ">", $date)
                            ->orWhereNull("product_ceilings.to_date")
                    );

                // Only add quantity constraints for INNER JOIN (non-STORES types)
                if ($this->distributionType !== DistributionType::STORES) {
                    $join->where(function ($q) {
                        $q->whereColumn('sales.quantity', '>=', 'product_ceilings.negative_units')
                            ->whereColumn('sales.quantity', '<=', 'product_ceilings.units');
                    });
                } else {
                    Log::debug('SalesService: Skipping quantity constraints for STORES distribution type (LEFT JOIN)');
                }
            });
        } else {
            Log::debug('SalesService: Skipping product ceilings join for non-NORMAL sale distribution', [
                'sale_distribution' => $this->saleDistribution->name
            ]);
        }

        Log::debug('SalesService: Query joins completed');
    }

    private function getCeiling(): array
    {
        $ceilings = match ($this->saleDistribution) {
            SaleDistribution::NORMAL => [Ceiling::BELOW],
            SaleDistribution::DIRECT => [Ceiling::BELOW, Ceiling::DISTRIBUTED]
        };

        return $ceilings;
    }
}
