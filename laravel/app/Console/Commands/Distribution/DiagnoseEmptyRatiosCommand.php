<?php

namespace App\Console\Commands\Distribution;

use App\Sale;
use App\Services\SalesService;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Enums\SaleDistribution;
use App\Services\Enums\Ceiling;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class DiagnoseEmptyRatiosCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'distribution:diagnose-empty-ratios 
                            {date : Date in YYYY-MM-DD format}
                            {product_id : Product ID}
                            {distributor_ids : Comma-separated distributor IDs}
                            {--division-ids= : Optional comma-separated division IDs}
                            {--show-raw-query : Show the raw SQL query}
                            {--show-data-samples : Show sample data from each table}';

    /**
     * The console command description.
     */
    protected $description = 'Diagnose why getRatiosForDistribution returns empty results for specific parameters';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $date = $this->argument('date');
        $productId = (int) $this->argument('product_id');
        $distributorIds = array_map('intval', explode(',', $this->argument('distributor_ids')));
        $divisionIds = $this->option('division-ids') 
            ? array_map('intval', explode(',', $this->option('division-ids')))
            : [];

        $this->info("=== DIAGNOSING EMPTY RATIOS ISSUE ===");
        $this->info("Date: {$date}");
        $this->info("Product ID: {$productId}");
        $this->info("Distributor IDs: " . implode(', ', $distributorIds));
        if (!empty($divisionIds)) {
            $this->info("Division IDs: " . implode(', ', $divisionIds));
        }
        $this->newLine();

        try {
            $parsedDate = Carbon::parse($date);
        } catch (\Exception $e) {
            $this->error("Invalid date format: {$date}");
            return 1;
        }

        // Step 1: Check basic data existence
        $this->info("1. CHECKING BASIC DATA EXISTENCE");
        $this->checkBasicDataExistence($parsedDate, $productId, $distributorIds);
        $this->newLine();

        // Step 2: Test the actual SalesService query
        $this->info("2. TESTING SALESSERVICE QUERY");
        $this->testSalesServiceQuery($date, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Step 3: Break down the query step by step
        $this->info("3. STEP-BY-STEP QUERY BREAKDOWN");
        $this->stepByStepQueryBreakdown($parsedDate, $productId, $distributorIds, $divisionIds);
        $this->newLine();

        // Step 4: Show sample data if requested
        if ($this->option('show-data-samples')) {
            $this->info("4. SAMPLE DATA FROM TABLES");
            $this->showSampleData($parsedDate, $productId, $distributorIds);
            $this->newLine();
        }

        return 0;
    }

    private function checkBasicDataExistence(Carbon $date, int $productId, array $distributorIds): void
    {
        // Check sales table
        $salesCount = Sale::where('date', $date->format('Y-m-d'))
            ->where('product_id', $productId)
            ->whereIn('distributor_id', $distributorIds)
            ->count();
        
        $this->line("Sales for date/product/distributors: {$salesCount}");

        // Check sales with BELOW ceiling
        $belowSalesCount = Sale::where('date', $date->format('Y-m-d'))
            ->where('product_id', $productId)
            ->whereIn('distributor_id', $distributorIds)
            ->where('ceiling', Ceiling::BELOW->value)
            ->count();
        
        $this->line("Sales with BELOW ceiling: {$belowSalesCount}");

        // Check sales with details
        $salesWithDetailsCount = Sale::where('date', $date->format('Y-m-d'))
            ->where('product_id', $productId)
            ->whereIn('distributor_id', $distributorIds)
            ->whereHas('details')
            ->count();
        
        $this->line("Sales with details: {$salesWithDetailsCount}");

        // Check sales with mappings
        $salesWithMappingsCount = Sale::where('date', $date->format('Y-m-d'))
            ->where('product_id', $productId)
            ->whereIn('distributor_id', $distributorIds)
            ->whereHas('mappings')
            ->count();
        
        $this->line("Sales with mappings: {$salesWithMappingsCount}");

        // Check product ceilings
        $productCeilingsCount = DB::table('product_ceilings')
            ->where('product_id', $productId)
            ->where('from_date', '<=', $date)
            ->where(function($q) use ($date) {
                $q->where('to_date', '>', $date)
                  ->orWhereNull('to_date');
            })
            ->count();
        
        $this->line("Active product ceilings: {$productCeilingsCount}");
    }

    private function testSalesServiceQuery(string $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        // Test with STORES distribution type
        $salesService = SalesService::make(SaleDistribution::NORMAL, DistributionType::STORES);
        
        $this->line("Testing SalesService with STORES distribution type...");
        
        $ratios = $salesService->getRatiosForDistribution($date, $productId, $distributorIds, $divisionIds);
        
        $this->line("Ratios count: " . $ratios->count());
        $this->line("Total percentage: " . $ratios->sum('percentage'));
        
        if ($ratios->count() > 0) {
            $this->line("Sample ratios:");
            foreach ($ratios->take(3) as $ratio) {
                $this->line("  - Div: {$ratio->div_id}, Line: {$ratio->line_id}, Brick: {$ratio->brick_id}, %: {$ratio->percentage}");
            }
        } else {
            $this->warn("No ratios found - this is the issue we're investigating");
        }
    }

    private function stepByStepQueryBreakdown(Carbon $date, int $productId, array $distributorIds, array $divisionIds): void
    {
        $this->line("Building query step by step to identify where data is lost...");

        // Step 1: Start with basic sales
        $baseQuery = Sale::where('sales.date', $date->format('Y-m-d'))
            ->where('sales.product_id', $productId)
            ->whereIn('sales.distributor_id', $distributorIds);

        $baseCount = $baseQuery->count();
        $this->line("Step 1 - Base sales: {$baseCount}");

        // Step 2: Add ceiling filter
        $ceilingQuery = clone $baseQuery;
        $ceilingQuery->where('sales.ceiling', Ceiling::BELOW->value);
        $ceilingCount = $ceilingQuery->count();
        $this->line("Step 2 - With BELOW ceiling: {$ceilingCount}");

        // Step 3: Add sales_details join
        $detailsQuery = clone $ceilingQuery;
        $detailsQuery->join('sales_details', 'sales.id', '=', 'sales_details.sale_id');
        $detailsCount = $detailsQuery->count();
        $this->line("Step 3 - With sales_details join: {$detailsCount}");

        // Step 4: Add line_products join
        $lineProductsQuery = clone $detailsQuery;
        $lineProductsQuery->join('line_products', 'sales.product_id', '=', 'line_products.product_id');
        $lineProductsCount = $lineProductsQuery->count();
        $this->line("Step 4 - With line_products join: {$lineProductsCount}");

        // Step 5: Add line_divisions join
        $lineDivisionsQuery = clone $lineProductsQuery;
        $lineDivisionsQuery->join('line_divisions', function ($join) use ($date, $divisionIds) {
            $join->on('sales_details.div_id', '=', 'line_divisions.id')
                ->whereColumn('line_divisions.line_id', 'line_products.line_id')
                ->where('line_divisions.from_date', '<=', $date)
                ->where(function($q) use ($date) {
                    $q->where('line_divisions.to_date', '>', $date)
                      ->orWhereNull('line_divisions.to_date');
                });

            if (!empty($divisionIds)) {
                $join->whereIn('line_divisions.id', $divisionIds);
            }
        });
        $lineDivisionsCount = $lineDivisionsQuery->count();
        $this->line("Step 5 - With line_divisions join: {$lineDivisionsCount}");

        // Step 6: Add mappings join for STORES
        $mappingsQuery = clone $lineDivisionsQuery;
        $mappingsQuery->join('mapping_sale', 'sales.id', '=', 'mapping_sale.sale_id')
            ->join('mappings', function ($join) {
                $join->on('mapping_sale.mapping_id', '=', 'mappings.id')
                    ->where('mappings.unified_pharmacy_type_id', DistributionType::STORES->value)
                    ->where('mappings.exception', false);
            });
        $mappingsCount = $mappingsQuery->count();
        $this->line("Step 6 - With STORES mappings join: {$mappingsCount}");

        // Step 7: Add product_ceilings LEFT JOIN (for STORES)
        $productCeilingsQuery = clone $mappingsQuery;
        $productCeilingsQuery->leftJoin('product_ceilings', function ($join) use ($date) {
            $join->on('product_ceilings.product_id', '=', 'sales.product_id')
                ->where('product_ceilings.from_date', '<=', $date)
                ->where(function($q) use ($date) {
                    $q->where('product_ceilings.to_date', '>', $date)
                      ->orWhereNull('product_ceilings.to_date');
                });
        });
        $productCeilingsCount = $productCeilingsQuery->count();
        $this->line("Step 7 - With product_ceilings LEFT JOIN: {$productCeilingsCount}");

        if ($this->option('show-raw-query')) {
            $this->newLine();
            $this->line("Final query SQL:");
            $this->line($productCeilingsQuery->toSql());
            $this->line("Bindings: " . json_encode($productCeilingsQuery->getBindings()));
        }
    }

    private function showSampleData(Carbon $date, int $productId, array $distributorIds): void
    {
        $this->line("Sample sales data:");
        $sales = Sale::where('date', $date->format('Y-m-d'))
            ->where('product_id', $productId)
            ->whereIn('distributor_id', $distributorIds)
            ->with(['details', 'mappings'])
            ->take(3)
            ->get();
        
        foreach ($sales as $sale) {
            $this->line("  Sale ID: {$sale->id}, Ceiling: {$sale->ceiling}, Details: {$sale->details->count()}, Mappings: {$sale->mappings->count()}");
        }
        
        $this->newLine();
        $this->line("Sample mappings with STORES type:");
        $mappings = DB::table('mappings')
            ->where('unified_pharmacy_type_id', DistributionType::STORES->value)
            ->where('exception', false)
            ->take(3)
            ->get();
        
        foreach ($mappings as $mapping) {
            $this->line("  Mapping ID: {$mapping->id}, Name: {$mapping->name}, Type: {$mapping->unified_pharmacy_type_id}");
        }
    }
}
