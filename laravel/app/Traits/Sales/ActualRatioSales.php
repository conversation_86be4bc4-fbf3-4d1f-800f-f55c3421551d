<?php

namespace App\Traits\Sales;

use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDistribution;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait ActualRatioSales
{

    /**
     * Get distribution ratios for sales based on specified parameters
     */
    public function getRatiosForDistribution(
        string            $date,
        int               $product,
        array             $distributorIds,
        array             $divisionIds = [],
        SaleDistribution  $saleDistribution = SaleDistribution::NORMAL,
        ?DistributionType $distributionType = DistributionType::STORES
    ): Collection
    {

        // Validate input parameters
        if (empty($distributorIds)) {
            Log::warning('getRatiosForDistribution: Empty distributor IDs provided', [
                'date' => $date,
                'product_id' => $product,
                'context' => 'PARAMETER_VALIDATION'
            ]);
            return collect();
        }

        $date = Carbon::parse($date);

        DB::statement("set sql_mode=''");

        // Get configuration for this distribution type
        $config = $this->getSalesConfig($saleDistribution, $distributionType);

        Log::debug('getRatiosForDistribution: Starting query', [
            'date' => $date->format('Y-m-d'),
            'product_id' => $product,
            'distributor_ids' => $distributorIds,
            'division_ids' => $divisionIds,
            'sale_distribution' => $saleDistribution->name,
            'distribution_type' => $distributionType?->value,
            'context' => 'QUERY_START'
        ]);

        $query = Sale::query();
        $query->select($config['selections']);

        $this->buildSalesJoins($query, $date, $product, $divisionIds, $saleDistribution, $distributionType);

        $ceilings = $this->getSalesCeiling($saleDistribution);

        $query
            ->whereYear("sales.date", (string)$date->year)
            ->whereMonth("sales.date", (string)$date->month)
            ->whereIn("sales.distributor_id", $distributorIds)
            ->whereIn("sales.ceiling", $ceilings);

        $query->groupBy($config['groupBy']);

        try {
            $results = $query->get();
        } catch (\Exception $e) {
            Log::error('getRatiosForDistribution: Query execution failed', [
                'date' => $date->format('Y-m-d'),
                'product_id' => $product,
                'distributor_ids' => $distributorIds,
                'division_ids' => $divisionIds,
                'error' => $e->getMessage(),
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings(),
                'context' => 'QUERY_EXECUTION_ERROR'
            ]);
            return collect();
        }

        // Log detailed results for debugging
        Log::info('getRatiosForDistribution: Distribution ratios retrieved' . collect([
                'date' => $date->format('Y-m-d'),
                'product_id' => $product,
                'ratios_count' => $results->count(),
                'total_percentage' => $results->sum('percentage'),
                'distribution_type' => $distributionType?->value ?? 'default',
                'sale_distribution' => $saleDistribution->name,
                'sample_results' => $results->take(3)->toArray(),
                'context' => 'SUCCESSFUL_QUERY_EXECUTION'
            ])->toJson(JSON_PRETTY_PRINT));

        // If no results, log additional debugging info
        if ($results->count() === 0) {
            $this->logEmptyResultsDebugInfo($date, $product, $distributorIds, $divisionIds, $distributionType, $ceilings);
        }

        return $results;
    }

    /**
     * Get sales configuration (selections and groupBy) based on distribution settings
     */
   private function getSalesConfig(SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): array
    {
        $selections = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
            DB::raw('CAST(SUM(crm_sales_details.quantity) AS DECIMAL(64,20)) /
               SUM(SUM(crm_sales_details.quantity)) OVER (PARTITION BY crm_sales.product_id) AS percentage')
        ];

        $groupBy = [
            "sales_details.div_id",
            "sales_details.line_id",
            "sales_details.brick_id",
        ];

        Log::debug('getSalesConfig: Configuration generated', [
            'sale_distribution' => $saleDistribution->name,
            'distribution_type' => $distributionType?->value,
            'selections_count' => count($selections),
            'group_by_count' => count($groupBy),
            'context' => 'CONFIG_GENERATION'
        ]);

        return [
            'selections' => $selections,
            'groupBy' => $groupBy
        ];
    }

    /**
     * Build all necessary joins for the sales query
     */
    private function buildSalesJoins(
        $query,
        Carbon $date,
        int $product,
        array $divisionIds,
        SaleDistribution $saleDistribution,
        ?DistributionType $distributionType = null
    ): void
    {
        $query
            ->join("sales_details", "sales.id", "sales_details.sale_id")
            ->join("line_products", function ($join) use ($date, $product) {
                $join
                    ->on("sales.product_id", "=", "line_products.product_id")
                    ->where("line_products.product_id", $product)
                    ->whereColumn("line_products.line_id", "sales_details.line_id")
                    ->where("line_products.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_products.to_date", ">", $date)
                            ->orWhereNull("line_products.to_date")
                    );
            })
            ->join("line_divisions", function ($join) use ($date, $divisionIds) {
                $join
                    ->on("sales_details.div_id", "=", "line_divisions.id")
                    ->whereColumn("line_divisions.line_id", "line_products.line_id")
                    ->where("line_divisions.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("line_divisions.to_date", ">", $date)
                            ->orWhereNull("line_divisions.to_date")
                    );

                if (!empty($divisionIds)) {
                    $join->whereIn('line_divisions.id', $divisionIds);
                    Log::debug('buildSalesJoins: Applied division ID filter', [
                        'division_ids' => $divisionIds
                    ]);
                }
            });

        // Add mappings joins when DistributionType is specified
        if ($distributionType !== null) {
            $query
                ->join('mapping_sale', 'mapping_sale.sale_id', 'sales.id')
                ->join("mappings", function ($join) use ($distributionType) {
                    $join->on('mapping_sale.mapping_id', 'mappings.id');

                    if ($distributionType === DistributionType::PRIVATE_PHARMACY) {
                        Log::debug('buildSalesJoins: Applying PRIVATE_PHARMACY mapping filter (excluding STORES and LOCAL_CHAINS)');
                        $join->where(function ($q) {
                            $q->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::STORES->value)
                                ->where('mappings.unified_pharmacy_type_id', '!=', DistributionType::LOCAL_CHAINS->value)
                                ->orWhere('mappings.unified_pharmacy_type_id', null);
                        });
                    } else {
                        Log::debug('buildSalesJoins: Applying specific distribution type mapping filter', [
                            'distribution_type_value' => $distributionType->value
                        ]);
                        $join->where('mappings.unified_pharmacy_type_id', $distributionType->value);
                    }
                })
                ->where("mappings.exception", false);
        } else {
            Log::debug('buildSalesJoins: No distribution type specified, skipping mappings joins');
        }

        if ($saleDistribution == SaleDistribution::NORMAL) {
            // Use LEFT JOIN for STORES distribution type, INNER JOIN for others
            $joinType = ($distributionType === DistributionType::STORES) ? 'leftJoin' : 'join';

            $query->$joinType("product_ceilings", function ($join) use ($date, $distributionType) {
                $join
                    ->on("product_ceilings.product_id", "=", "sales.product_id")
                    ->where("product_ceilings.from_date", "<=", $date)
                    ->where(
                        fn($q) => $q
                            ->where("product_ceilings.to_date", ">", $date)
                            ->orWhereNull("product_ceilings.to_date")
                    );

                // Only add quantity constraints for INNER JOIN (non-STORES types)
                if ($distributionType !== DistributionType::STORES) {
                    $join->where(function ($q) {
                        $q->whereColumn('sales.quantity', '>=', 'product_ceilings.negative_units')
                            ->whereColumn('sales.quantity', '<=', 'product_ceilings.units');
                    });
                } else {
                    Log::debug('buildSalesJoins: Skipping quantity constraints for STORES distribution type (LEFT JOIN)');
                }
            });
        } else {
            Log::debug('buildSalesJoins: Skipping product ceilings join for non-NORMAL sale distribution', [
                'sale_distribution' => $saleDistribution->name
            ]);
        }

        Log::debug('buildSalesJoins: Query joins completed');
    }

    /**
     * Get ceiling values based on sale distribution type
     */
    private function getSalesCeiling(SaleDistribution $saleDistribution): array
    {
        return match ($saleDistribution) {
            SaleDistribution::NORMAL => [Ceiling::BELOW],
            SaleDistribution::DIRECT => [Ceiling::BELOW, Ceiling::DISTRIBUTED]
        };
    }

    /**
     * Log debugging information when no results are found
     */
    private function logEmptyResultsDebugInfo(
        Carbon            $date,
        int               $product,
        array             $distributorIds,
        array             $divisionIds,
        ?DistributionType $distributionType,
        array             $ceilings
    ): void
    {
        // Check if there are any sales at all for these parameters
        $salesCount = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->count();

        $salesWithDetailsCount = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->whereHas('details')
            ->count();

        $salesWithCorrectCeiling = Sale::where('product_id', $product)
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereIn('distributor_id', $distributorIds)
            ->whereIn('ceiling', array_map(fn($c) => $c->value, $ceilings))
            ->count();

        Log::warning('logEmptyResultsDebugInfo: No distribution ratios found - investigating' . collect([
                'date' => $date->format('Y-m-d'),
                'product_id' => $product,
                'distributor_ids' => $distributorIds,
                'division_ids' => $divisionIds,
                'distribution_type' => $distributionType?->value,
                'ceilings' => array_map(fn($c) => $c->value, $ceilings),
                'query_conditions' => [
                    'year' => $date->year,
                    'month' => $date->month,
                    'has_distribution_type' => $distributionType !== null,
                    'has_division_filter' => !empty($divisionIds)
                ],
                'database_state' => [
                    'total_sales_count' => $salesCount,
                    'sales_with_details_count' => $salesWithDetailsCount,
                    'sales_with_correct_ceiling_count' => $salesWithCorrectCeiling
                ],
                'context' => 'EMPTY_RESULTS_INVESTIGATION'
            ])->toJson(JSON_PRETTY_PRINT));
    }

    /**
     * Generate cache key for sales operations (if caching is needed)
     */
    private function generateSalesCacheKey(string $functionName, array $parameters, SaleDistribution $saleDistribution, ?DistributionType $distributionType = null): string
    {
        $cacheKeyData = [
            'function' => $functionName,
            'parameters' => $parameters,
            'distribution_type' => $distributionType?->value,
            'sale_distribution' => $saleDistribution->name,
            'timestamp' => time() // Add timestamp for cache invalidation
        ];

        $paramsString = serialize($cacheKeyData);
        $hashedKey = md5($functionName . '|' . $paramsString);

        Log::debug('generateSalesCacheKey: Generated cache key', [
            'function_name' => $functionName,
            'parameters_count' => count($parameters),
            'distribution_type' => $distributionType?->value,
            'sale_distribution' => $saleDistribution->name,
            'cache_key_hash' => $hashedKey
        ]);

        return $hashedKey;
    }
}
